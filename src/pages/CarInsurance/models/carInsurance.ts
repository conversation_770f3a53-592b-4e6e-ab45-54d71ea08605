/* eslint-disable react-hooks/exhaustive-deps */

import type { TgetRelationList } from '@/pages/CarInsuranceChannel/services';
import { getRelationList } from '@/pages/CarInsuranceChannel/services';
import type { UloanUserType } from '@/utils/bankend/enum';
import { EcarInsuranceStatus } from '@/utils/bankend/enum';
import { history, useModel } from '@umijs/max';
import { useEffect, useState } from 'react';
import { getCarInsuranceDetail, getChannelInfo, getOverdueCaseInfo } from '../services';
import type { IcarChannelItem, IcarInfoItem, IdetailData, IoptionsData } from '../type';
import { LEVEL } from '../type';
import { getRelationOptions } from '../utils';
// import { isCarInsuranceStoreUser } from '@/utils/utils';
export default () => {
  const [optionsData, setOptionsData] = useState<IoptionsData>({
    channelList: [],
    enterpriseList: [],
    productList: [],
    companyList: [],
  });
  const { initialState = {} } = useModel<any>('@@initialState');
  const { currentUser = {} } = initialState;
  const { channelCode: currentUserChannelCode, channelLevel } = currentUser;
  const [detailData, setDetailData] = useState<IdetailData>({} as IdetailData);
  const [isEditable, setIsEditable] = useState(true); // 是否可以编辑 有些状态是不能编辑 是禁用的的
  const [carInfo, setCarInfo] = useState<IcarInfoItem[]>([]);
  const [detailLoading, setDetailLoading] = useState(false);

  const search = new URLSearchParams(window.location.search);
  const multiplexOrderNo = search.get('multiplexOrderNo');

  const [isMultiplexOrder, setIsMultiplexOrder] = useState(false); // 是否为复用的订单

  const [borrowerType, setBorrowerType] = useState<UloanUserType>();

  const [channelList, setChannelList] = useState<IcarChannelItem[]>([]);
  const [relation, setRelation] = useState<TgetRelationList>();
  // 最大逾期天数
  const [overdueLongestDays, setOverdueLongestDays] = useState<undefined | number>(undefined);
  // const isChannelUser = isCarInsuranceStoreUser(access);
  // 获取逾期信息
  const initOverdueCaseInfo = async () => {
    // TODO:test同一个订单下不同状态切换是否正常
    setOverdueLongestDays(undefined);
    const { personalInfo, orderNo, enterpriseInfo } = detailData;
    if (!orderNo) {
      return;
    }
    const params: {
      idNo: string | number;
      type: 0 | 1;
    } = !!enterpriseInfo?.epAuthNo
      ? {
          idNo: enterpriseInfo?.epAuthNo,
          type: 0, // 查企业
        }
      : {
          idNo: personalInfo?.idNo,
          type: 1, // 查个人
        };
    const res = await getOverdueCaseInfo(params);

    if (res?.data?.hasOverdueCase) {
      setOverdueLongestDays(res?.data?.overdueCase?.overdueLongestDays ?? 0);
    }
  };

  useEffect(() => {
    setIsMultiplexOrder(!!multiplexOrderNo);
  }, [multiplexOrderNo]);

  useEffect(() => {
    const roleArr =
      currentUser?.role?.map((item: { roleCode: any }) => {
        return item?.roleCode;
      }) || [];
    //  console.log(roleArr)
    // 这里虽然没有访问对应页面, 但是却执行了这里   //融租渠道也会请求，会报错，加上融租判断不请求
    if (
      currentUser?.userId &&
      !(roleArr?.includes('leaseChannelUser') || roleArr?.includes('leaseStoreUser'))
    ) {
      // 登录了才去请求
      getChannelInfo({ channelCode: currentUserChannelCode, channelLevel }).then(setChannelList); // 获取所有的渠道列表
      getRelationList().then(setRelation); // 获取所有的产品 企业 公司 信息
    }
  }, [currentUser?.userId]);

  // 假如登陆的用户是渠道用户 那么 渠道只有一个 ，是默认选上的，且是禁用状态，所以应该关联出对应的产品，公司，企业
  // 如果是渠道用户 无论是 复用 新增 编辑 都只有一个渠道 对应的关联信息一定是确定的
  // 当编辑获取复用的时候 会调用detail接口，到时候再传入 channelCode 等数据

  // 获取新增的时候 渠道和非渠道用户的 渠道，产品，公司，企业 的options
  function getOptions() {
    // const isChannelUser = role
    //   .map((item: { roleCode: string }) => item.roleCode)
    //   .includes('channelUser');

    if (channelList?.length && relation) {
      if (channelLevel === LEVEL.SECOND_CHANNEL) {
        // 如果是二级渠道用户，且所有的渠道列表 和 所有需要关联的配置信息 都存在
        const currentChannel = channelList.find(
          (item) => item.channelCode === currentUserChannelCode,
        );
        const { channelCode = '' } = currentChannel || {};
        const options = getRelationOptions({
          channelCode,
          channelList,
          relation,
          borrowerType,
        });
        // 设置所有的 渠道 产品 公司 企业 的select 选项
        setOptionsData({
          channelList: [currentChannel!],
          ...options,
        });
      } else {
        // 非渠道用户 展示所有的渠道
        setOptionsData({
          channelList, // 非渠道用户 展示所有的渠道
          enterpriseList: [], // 当 channel 更改时才有
          productList: [],
          companyList: [],
        });
      }
    }
  }

  useEffect(() => {
    getOptions();
    console.log(relation);
  }, [channelList, relation, currentUser]);

  // 根据不同的channelCode 和 借款人类型 设置关联的产品 公司 企业 信息
  function setRelationOptions(params: {
    channelCode: string;
    borrowerType: UloanUserType;
    productCode?: string;
  }) {
    // console.log('paramsparamsparamsparams', params);
    const { channelCode, borrowerType, productCode } = params;
    const options = getRelationOptions({
      channelCode,
      channelList,
      relation,
      borrowerType,
      productCode,
    });
    setOptionsData((state) => {
      return {
        ...state,
        ...options,
      };
    });
  }

  // 获取详情
  async function getDetail(orderNo: string, options?: { noLoading?: boolean }) {
    try {
      // 兼容历史投保单和驾驶证 并将其转成前端的格式
      const { multiplexOrderNo } = (history as any).location.query;

      if (!options?.noLoading) {
        setDetailLoading(true);
      }
      const detailData1 = await getCarInsuranceDetail(orderNo, multiplexOrderNo ? true : undefined);

      const { baseInfo } = detailData1 || {};
      const { channelCode, borrowerType } = baseInfo || {};
      // 编辑的时候 设置 初始的选项
      setRelationOptions({ channelCode, borrowerType });

      if (multiplexOrderNo) {
        // 如果是复用 则需要重置一些数据
        // 状态重置为草稿
        detailData1.orderStatus = EcarInsuranceStatus.DRAFT;
        // 首付凭证信息
        detailData1.downPaymentsDTO = {} as any;

        // 重置 是否合并打款
        detailData1.insuranceInfo.mergePayFlag = null;
        // 操作记录
        detailData1.operateLogs = [];
        // 清除车辆信息的id // 将车辆的状态改为未检查
        detailData1.carInfoDTOList = detailData1?.carInfoDTOList?.map((car) => {
          return {
            ...car,
            id: undefined,
            inspected: 0, // 车辆的状态
          };
        });
      }
      // 兼容 历史单的 驾驶证和投保材料
      setDetailData(detailData1);
      setBorrowerType(detailData1?.baseInfo.borrowerType);
      // 根据当前的状态判断是否可以编辑
      const { orderStatus } = detailData1;
      const editableStatus: EcarInsuranceStatus[] = [
        EcarInsuranceStatus.DRAFT,
        EcarInsuranceStatus.REJECT,
      ];
      if (editableStatus.includes(orderStatus)) {
        // 只有草稿和驳回 表单可以随意修改
        setIsEditable(true);
      } else {
        // 其他状态均不能修改
        setIsEditable(false);
      }
      await initOverdueCaseInfo();
    } finally {
      setDetailLoading(false);
    }
  }

  // 批量新增后的车辆信息
  return {
    optionsData,
    detailData,
    getDetail,
    isEditable,
    setIsEditable,
    carInfo,
    setCarInfo,
    detailLoading,
    setDetailData,
    setRelationOptions,
    borrowerType,
    setBorrowerType,
    channelList,
    relation,
    setChannelList,
    overdueLongestDays,
    isMultiplexOrder,
  };
};
